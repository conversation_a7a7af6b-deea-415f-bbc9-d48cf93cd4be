"use strict";(()=>{var e={};e.id=534,e.ids=[534],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},85158:e=>{e.exports=require("http2")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},32285:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>m,originalPathname:()=>h,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>DELETE,OPTIONS:()=>OPTIONS,PUT:()=>PUT}),t(78976);var o=t(10884),a=t(16132),n=t(95798),i=t(37723),u=t(83479);async function DELETE(e,{params:r}){let{username:t,chatId:s,messageId:o}=r;if(!t||!s||!o)return n.Z.json({error:"Username, chatId e messageId s\xe3o obrigat\xf3rios"},{status:400});try{let e=(0,i.iH)(u.tO,`usuarios/${t}/conversas/${s}/chat.json`),r=await (0,i.Jt)(e),a=await fetch(r);if(!a.ok)throw Error(`Erro ao buscar arquivo: ${a.statusText}`);let c=await a.json(),l=c.messages.filter(e=>e.id!==o),p={...c,messages:l,lastUpdated:new Date().toISOString()},d=new Blob([JSON.stringify(p,null,2)],{type:"application/json"});return await (0,i.KV)(e,d),n.Z.json({success:!0,message:"Mensagem deletada com sucesso"},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Erro ao deletar mensagem:",e),n.Z.json({error:"Erro interno do servidor"},{status:500})}}async function PUT(e,{params:r}){let{username:t,chatId:s,messageId:o}=r;if(!t||!s||!o)return n.Z.json({error:"Username, chatId e messageId s\xe3o obrigat\xf3rios"},{status:400});try{let r=await e.json(),{content:a}=r;if(!a||"string"!=typeof a)return n.Z.json({error:"Conte\xfado da mensagem \xe9 obrigat\xf3rio"},{status:400});let c=(0,i.iH)(u.tO,`usuarios/${t}/conversas/${s}/chat.json`),l=await (0,i.Jt)(c),p=await fetch(l);if(!p.ok)throw Error(`Erro ao buscar arquivo: ${p.statusText}`);let d=await p.json(),m=d.messages.map(e=>e.id===o?{...e,content:a.trim(),timestamp:new Date().toISOString()}:e),g={...d,messages:m,lastUpdated:new Date().toISOString()},h=new Blob([JSON.stringify(g,null,2)],{type:"application/json"});return await (0,i.KV)(c,h),n.Z.json({success:!0,message:"Mensagem atualizada com sucesso"},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Erro ao atualizar mensagem:",e),n.Z.json({error:"Erro interno do servidor"},{status:500})}}async function OPTIONS(){return n.Z.json({},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/chat/[username]/[chatId]/message/[messageId]/route",pathname:"/api/chat/[username]/[chatId]/message/[messageId]",filename:"route",bundlePath:"app/api/chat/[username]/[chatId]/message/[messageId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\api\\chat\\[username]\\[chatId]\\message\\[messageId]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:d,headerHooks:m,staticGenerationBailout:g}=c,h="/api/chat/[username]/[chatId]/message/[messageId]/route"}};var r=require("../../../../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[955,818,479],()=>__webpack_exec__(32285));module.exports=t})();