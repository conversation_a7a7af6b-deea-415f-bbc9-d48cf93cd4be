"use strict";(()=>{var e={};e.id=836,e.ids=[836],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},34269:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>d,originalPathname:()=>m,requestAsyncStorage:()=>c,routeModule:()=>a,serverHooks:()=>l,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>u});var r={};o.r(r),o.d(r,{GET:()=>GET,OPTIONS:()=>OPTIONS}),o(78976);var s=o(10884),n=o(16132),i=o(95798);async function GET(e){try{let{searchParams:t}=new URL(e.url),o=t.get("category"),r="https://openrouter.ai/api/v1/models";o&&(r+=`?category=${encodeURIComponent(o)}`);let s=await fetch(r,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Erro da API OpenRouter: ${s.status} ${s.statusText}`);let n=await s.json(),a=n.data.map(e=>({id:e.id,name:e.name,description:e.description,contextLength:e.context_length||e.top_provider.context_length,maxCompletionTokens:e.top_provider.max_completion_tokens,isModerated:e.top_provider.is_moderated,pricing:{prompt:parseFloat(e.pricing.prompt)||0,completion:parseFloat(e.pricing.completion)||0,isFree:"0"===e.pricing.prompt&&"0"===e.pricing.completion},inputModalities:e.architecture.input_modalities,outputModalities:e.architecture.output_modalities,tokenizer:e.architecture.tokenizer,instructType:e.architecture.instruct_type,huggingFaceId:e.hugging_face_id,supportedParameters:e.supported_parameters})),c=a.sort((e,t)=>e.pricing.isFree&&!t.pricing.isFree?-1:!e.pricing.isFree&&t.pricing.isFree?1:e.name.localeCompare(t.name));return i.Z.json({success:!0,models:c,total:c.length},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type","Cache-Control":"public, max-age=3600"}})}catch(e){return console.error("Erro ao buscar modelos da OpenRouter:",e),i.Z.json({success:!1,error:"Erro ao buscar modelos da OpenRouter",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}}async function OPTIONS(){return new i.Z(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let a=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/models/route",pathname:"/api/models",filename:"route",bundlePath:"app/api/models/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\api\\models\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:c,staticGenerationAsyncStorage:p,serverHooks:l,headerHooks:d,staticGenerationBailout:u}=a,m="/api/models/route"}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),o=t.X(0,[955],()=>__webpack_exec__(34269));module.exports=o})();