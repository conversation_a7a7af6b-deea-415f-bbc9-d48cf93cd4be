"use strict";(()=>{var e={};e.id=610,e.ids=[610],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51501:(e,o,r)=>{r.r(o),r.d(o,{headerHooks:()=>d,originalPathname:()=>A,requestAsyncStorage:()=>c,routeModule:()=>i,serverHooks:()=>l,staticGenerationAsyncStorage:()=>u,staticGenerationBailout:()=>p});var t={};r.r(t),r.d(t,{OPTIONS:()=>OPTIONS,POST:()=>POST}),r(78976);var s=r(10884),a=r(16132),n=r(95798);async function POST(e){try{let{apiKey:o}=await e.json();if(console.log("API Route - Recebida requisi\xe7\xe3o para buscar cr\xe9ditos"),console.log("API Key recebida:",o?`${o.substring(0,10)}...`:"N\xe3o fornecida"),!o)return console.log("API Route - Erro: API key n\xe3o fornecida"),n.Z.json({error:"API key \xe9 obrigat\xf3ria"},{status:400});console.log("API Route - Fazendo requisi\xe7\xe3o para OpenRouter...");let r=await fetch("https://openrouter.ai/api/v1/credits",{method:"GET",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}});if(console.log("API Route - Status da resposta:",r.status),!r.ok){let e=await r.text();throw console.log("API Route - Erro da OpenRouter:",e),Error(`Erro da API OpenRouter: ${r.status} ${r.statusText}`)}let t=await r.json();console.log("API Route - Dados recebidos da OpenRouter:",t);let{total_credits:s,total_usage:a}=t.data,i=s-a;return console.log("API Route - Cr\xe9ditos calculados:",{total_credits:s,total_usage:a,balance:i}),n.Z.json({success:!0,balance:i,total_credits:s,total_usage:a},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Erro ao buscar cr\xe9ditos da OpenRouter:",e),n.Z.json({success:!1,error:"Erro ao buscar cr\xe9ditos da OpenRouter",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}}async function OPTIONS(){return n.Z.json({},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let i=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/openrouter/credits/route",pathname:"/api/openrouter/credits",filename:"route",bundlePath:"app/api/openrouter/credits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\api\\openrouter\\credits\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:c,staticGenerationAsyncStorage:u,serverHooks:l,headerHooks:d,staticGenerationBailout:p}=i,A="/api/openrouter/credits/route"}};var o=require("../../../../webpack-runtime.js");o.C(e);var __webpack_exec__=e=>o(o.s=e),r=o.X(0,[955],()=>__webpack_exec__(51501));module.exports=r})();