(()=>{var e={};e.id=931,e.ids=[931],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},30130:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(67096),i=r(16132),o=r(37284),a=r.n(o),n=r(32564),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68203)),"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,42540)),"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\page.tsx"],u="/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42603:(e,t,r)=>{Promise.resolve().then(r.bind(r,97429))},97429:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Home});var s=r(30784),i=r(9885),o=r(11440),a=r.n(o),n=r(57114),l=r(78157),c=r(29904),d=r(72373);function Home(){let{user:e,logout:t,loading:r}=(0,l.useAuth)(),o=(0,n.useRouter)();(0,i.useEffect)(()=>{!r&&e&&o.push("/dashboard")},[e,r,o]);let testFirestore=async()=>{try{console.log("Testando conex\xe3o com Firestore..."),await (0,c.pl)((0,c.doc)(d.db,"test","test-doc"),{message:"Teste de conex\xe3o",timestamp:new Date().toISOString()}),alert("Teste do Firestore bem-sucedido! Verifique o console."),console.log("Documento de teste criado com sucesso!")}catch(e){console.error("Erro no teste do Firestore:",e),alert("Erro no teste do Firestore. Verifique o console.")}};return r?s.jsx("main",{className:"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),s.jsx("p",{className:"text-white/80",children:"Carregando..."})]})}):(0,s.jsxs)("main",{className:"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[s.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"}),s.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"}),s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"})]}),(0,s.jsxs)("div",{className:"absolute top-8 left-8 right-8 flex justify-between items-center z-10",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"Rafthor"}),s.jsx("p",{className:"text-white/70 text-sm",children:"AI Chatbot Platform"})]}),s.jsx("div",{className:"flex items-center space-x-4",children:e?(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-white/90",children:["Ol\xe1, ",e.email]}),s.jsx("button",{onClick:t,className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"Sair"})]}):s.jsx(a(),{href:"/login",className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"Entrar"})})]}),(0,s.jsxs)("div",{className:"relative z-10 text-center",children:[s.jsx("h1",{className:"text-5xl font-bold text-white mb-6",children:"Bem-vindo ao Rafthor"}),s.jsx("p",{className:"text-xl text-white/80 mb-8",children:"Plataforma de chatbot com m\xfaltiplas IAs"}),e?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-white/90 text-lg",children:["Voc\xea est\xe1 logado como: ",s.jsx("strong",{children:e.email})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx("button",{onClick:()=>o.push("/dashboard"),className:"bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Iniciar Chat"}),s.jsx("button",{className:"bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Configura\xe7\xf5es"}),s.jsx("button",{onClick:testFirestore,className:"bg-red-500/20 hover:bg-red-500/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-red-500/30",children:"Testar Firestore"})]})]}):s.jsx(a(),{href:"/login",className:"inline-block bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Entrar"})]})]})}},68203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>o,default:()=>l});var s=r(95153);let i=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\app\page.tsx`),{__esModule:o,$$typeof:a}=i,n=i.default,l=n}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[332,322,238],()=>__webpack_exec__(30130));module.exports=r})();