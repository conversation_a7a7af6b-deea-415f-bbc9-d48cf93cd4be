(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{8366:function(e,t,s){Promise.resolve().then(s.bind(s,7355))},7355:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return LoginPage}});var r=s(7437),a=s(2265),o=s(8081),i=s(4086),n=s(5813),l=s(6831),c=s(4033),d=s(3549);function LoginPage(){let[e,t]=(0,a.useState)("login"),[s,u]=(0,a.useState)({username:"",email:"",password:""}),[h,x]=(0,a.useState)(!1),[m,p]=(0,a.useState)(""),[f,v]=(0,a.useState)(!1),w=(0,c.useRouter)(),{user:b,loading:g}=(0,d.useAuth)();if((0,a.useEffect)(()=>{!g&&b&&w.push("/")},[b,g,w]),g)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white/80",children:"Carregando..."})]})});if(b)return null;let handleInputChange=e=>{u({...s,[e.target.name]:e.target.value})},handleSubmit=async t=>{t.preventDefault(),x(!0),p("");try{if("register"===e){if(!s.username.trim()){p("Nome de usu\xe1rio \xe9 obrigat\xf3rio");return}console.log("Criando usu\xe1rio no Firebase Auth...");let e=await (0,o.Xb)(l.I8,s.email,s.password);console.log("Usu\xe1rio criado no Auth:",e.user.uid),console.log("Criando documento no Firestore...");let t={id:e.user.uid,username:s.username,email:s.email,balance:0,createdAt:new Date().toISOString()};console.log("Dados do usu\xe1rio:",t),console.log("Caminho do documento:","usuarios/".concat(s.username)),await (0,i.pl)((0,i.doc)(l.db,"usuarios",s.username),t),console.log("Documento criado no Firestore com sucesso!"),console.log("Criando configura\xe7\xf5es padr\xe3o..."),await (0,i.pl)((0,i.doc)(l.db,"usuarios",s.username,"configuracoes","settings"),{aparencia:{fonte:"Inter",tamanhoFonte:14,palavrasPorSessao:5e3},endpoints:{OpenRouter:{nome:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",modeloPadrao:"meta-llama/llama-3.1-8b-instruct:free",ativo:!1},DeepSeek:{nome:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",modeloPadrao:"deepseek-chat",ativo:!1}},memorias:{},categorias:{}}),console.log("Configura\xe7\xf5es padr\xe3o criadas com sucesso!");try{console.log("Criando estrutura de pastas no Storage...");let e=(0,n.iH)(l.tO,"usuarios/".concat(s.username,"/.keep")),t=new Uint8Array(0);await (0,n.KV)(e,t),console.log("Estrutura de pastas criada no Storage com sucesso!")}catch(e){console.error("Erro ao criar estrutura no Storage:",e)}alert("Usu\xe1rio registrado com sucesso! Verifique o Firestore."),w.push("/")}else await (0,o.e5)(l.I8,s.email,s.password),console.log("Login realizado com sucesso!"),w.push("/")}catch(e){console.error("Erro completo:",e),console.error("C\xf3digo do erro:",e.code),console.error("Mensagem do erro:",e.message);p("auth/email-already-in-use"===e.code?"Este email j\xe1 est\xe1 em uso":"auth/weak-password"===e.code?"A senha deve ter pelo menos 6 caracteres":"auth/invalid-email"===e.code?"Email inv\xe1lido":"auth/user-not-found"===e.code?"Usu\xe1rio n\xe3o encontrado":"auth/wrong-password"===e.code?"Senha incorreta":"auth/invalid-credential"===e.code?"Credenciais inv\xe1lidas":"permission-denied"===e.code?"Permiss\xe3o negada para escrever no Firestore. Verifique as regras de seguran\xe7a.":"unavailable"===e.code?"Servi\xe7o do Firestore indispon\xedvel. Tente novamente.":"Erro: ".concat(e.message))}finally{x(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-rafthor relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0",children:[(0,r.jsxs)("div",{className:"absolute top-0 left-0 w-full h-full",children:[(0,r.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"})]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)",backgroundSize:"20px 20px"}})})]}),(0,r.jsxs)("div",{className:"relative z-10 flex items-center justify-between p-4 sm:p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-white",children:"Rafthor"}),(0,r.jsx)("p",{className:"text-white/60 text-xs sm:text-sm",children:"AI Chatbot Platform"})]})]}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Vers\xe3o Beta"}),(0,r.jsx)("p",{className:"text-white/40 text-xs",children:"v1.0.0"})]})})]}),(0,r.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-[calc(100vh-200px)] px-4 py-8",children:(0,r.jsx)("div",{className:"w-full max-w-md",children:(0,r.jsx)("div",{className:"w-full max-w-md mx-auto px-4 sm:px-0 animate-slide-in-up",children:(0,r.jsxs)("div",{className:"relative bg-white/5 backdrop-blur-xl rounded-3xl p-6 sm:p-8 shadow-2xl border border-white/10 overflow-hidden hover-lift",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5"}),(0,r.jsx)("div",{className:"absolute -top-20 -right-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{className:"absolute -bottom-20 -left-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 backdrop-blur-sm border border-white/20 animate-float",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-white mb-2 tracking-tight",children:"login"===e?"Bem-vindo de volta":"Criar sua conta"}),(0,r.jsx)("p",{className:"text-white/60 text-sm sm:text-base",children:"login"===e?"Entre na sua conta do Rafthor":"Junte-se \xe0 revolu\xe7\xe3o da IA"})]}),(0,r.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-5",children:["register"===e&&(0,r.jsxs)("div",{className:"group",children:[(0,r.jsx)("label",{htmlFor:"username",className:"block text-sm font-semibold text-white/90 mb-3",children:"Nome de usu\xe1rio"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,r.jsx)("input",{type:"text",id:"username",name:"username",value:s.username,onChange:handleInputChange,required:!0,className:"w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base",placeholder:"Escolha um nome de usu\xe1rio"})]})]}),(0,r.jsxs)("div",{className:"group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-white/90 mb-3",children:"login"===e?"Email":"Endere\xe7o de email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"})})}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:s.email,onChange:handleInputChange,required:!0,className:"w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-white/90 mb-3",children:"Senha"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,r.jsx)("input",{type:f?"text":"password",id:"password",name:"password",value:s.password,onChange:handleInputChange,required:!0,className:"w-full pl-12 pr-12 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base",placeholder:"Crie uma senha segura"}),(0,r.jsx)("button",{type:"button",onClick:()=>v(!f),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-white/40 hover:text-white/70 transition-colors",children:f?(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):(0,r.jsxs)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),m&&(0,r.jsx)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-2xl p-4 backdrop-blur-sm animate-fade-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-200 text-sm",children:m})]})}),(0,r.jsx)("button",{type:"submit",disabled:h,className:"group relative w-full bg-gradient-to-r from-white/20 to-white/10 hover:from-white/30 hover:to-white/20 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm border border-white/20 hover:border-white/30 transform hover:scale-[1.02] active:scale-[0.98]",children:(0,r.jsx)("div",{className:"flex items-center justify-center space-x-3",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:"Processando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"login"===e?"Entrar na conta":"Criar minha conta"}),(0,r.jsx)("svg",{className:"h-5 w-5 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})})})]}),(0,r.jsxs)("div",{className:"relative my-8",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-white/10"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-4 bg-gradient-rafthor text-white/50",children:"login"===e?"Novo por aqui?":"J\xe1 tem uma conta?"})})]}),(0,r.jsx)("button",{onClick:()=>{t("login"===e?"register":"login"),p("")},className:"w-full text-center py-3 px-4 rounded-2xl border border-white/20 text-white/80 hover:text-white hover:bg-white/5 hover:border-white/30 transition-all duration-300 font-medium backdrop-blur-sm",children:"login"===e?(0,r.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),(0,r.jsx)("span",{children:"Criar nova conta"})]}):(0,r.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),(0,r.jsx)("span",{children:"Entrar na minha conta"})]})})]})]})})})}),(0,r.jsx)("div",{className:"relative z-10 p-4 sm:p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-white/40 text-xs sm:text-sm",children:"\xa9 2025 Rafthor. Plataforma de chatbot com m\xfaltiplas IAs."}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4 mt-2",children:[(0,r.jsx)("a",{href:"#",className:"text-white/30 hover:text-white/60 transition-colors text-xs",children:"Privacidade"}),(0,r.jsx)("span",{className:"text-white/20",children:"•"}),(0,r.jsx)("a",{href:"#",className:"text-white/30 hover:text-white/60 transition-colors text-xs",children:"Termos"}),(0,r.jsx)("span",{className:"text-white/20",children:"•"}),(0,r.jsx)("a",{href:"#",className:"text-white/30 hover:text-white/60 transition-colors text-xs",children:"Suporte"})]})]})})]})}},3549:function(e,t,s){"use strict";s.r(t),s.d(t,{AuthProvider:function(){return AuthProvider},useAuth:function(){return useAuth}});var r=s(7437),a=s(2265),o=s(8081),i=s(6831);let n=(0,a.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=e=>{let{children:t}=e,[s,l]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,o.Aj)(i.I8,e=>{l(e),d(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,o.w7)(i.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return(0,r.jsx)(n.Provider,{value:{user:s,loading:c,logout},children:t})}},6831:function(e,t,s){"use strict";s.d(t,{I8:function(){return d},db:function(){return u},tO:function(){return h}});var r=s(994),a=s(8081),o=s(4086),i=s(5813),n=s(3216);let l={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!l.apiKey||l.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let c=(0,r.ZF)(l),d=(0,a.v0)(c),u=(0,o.ad)(c),h=(0,i.cF)(c);(0,n.$C)(c)},4033:function(e,t,s){e.exports=s(94)}},function(e){e.O(0,[609,15,14,971,472,744],function(){return e(e.s=8366)}),_N_E=e.O()}]);