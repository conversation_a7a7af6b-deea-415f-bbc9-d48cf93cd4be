(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{7779:function(e,t,r){Promise.resolve().then(r.bind(r,5721))},5721:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Home}});var s=r(7437),o=r(2265),a=r(1396),i=r.n(a),l=r(4033),n=r(3549),d=r(4086),c=r(6831);function Home(){let{user:e,logout:t,loading:r}=(0,n.useAuth)(),a=(0,l.useRouter)();(0,o.useEffect)(()=>{!r&&e&&a.push("/dashboard")},[e,r,a]);let testFirestore=async()=>{try{console.log("Testando conex\xe3o com Firestore..."),await (0,d.pl)((0,d.doc)(c.db,"test","test-doc"),{message:"Teste de conex\xe3o",timestamp:new Date().toISOString()}),alert("Teste do Firestore bem-sucedido! Verifique o console."),console.log("Documento de teste criado com sucesso!")}catch(e){console.error("Erro no teste do Firestore:",e),alert("Erro no teste do Firestore. Verifique o console.")}};return r?(0,s.jsx)("main",{className:"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white/80",children:"Carregando..."})]})}):(0,s.jsxs)("main",{className:"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"})]}),(0,s.jsxs)("div",{className:"absolute top-8 left-8 right-8 flex justify-between items-center z-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Rafthor"}),(0,s.jsx)("p",{className:"text-white/70 text-sm",children:"AI Chatbot Platform"})]}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:e?(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-white/90",children:["Ol\xe1, ",e.email]}),(0,s.jsx)("button",{onClick:t,className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"Sair"})]}):(0,s.jsx)(i(),{href:"/login",className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"Entrar"})})]}),(0,s.jsxs)("div",{className:"relative z-10 text-center",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold text-white mb-6",children:"Bem-vindo ao Rafthor"}),(0,s.jsx)("p",{className:"text-xl text-white/80 mb-8",children:"Plataforma de chatbot com m\xfaltiplas IAs"}),e?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-white/90 text-lg",children:["Voc\xea est\xe1 logado como: ",(0,s.jsx)("strong",{children:e.email})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)("button",{onClick:()=>a.push("/dashboard"),className:"bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Iniciar Chat"}),(0,s.jsx)("button",{className:"bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Configura\xe7\xf5es"}),(0,s.jsx)("button",{onClick:testFirestore,className:"bg-red-500/20 hover:bg-red-500/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-red-500/30",children:"Testar Firestore"})]})]}):(0,s.jsx)(i(),{href:"/login",className:"inline-block bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30",children:"Entrar"})]})]})}},3549:function(e,t,r){"use strict";r.r(t),r.d(t,{AuthProvider:function(){return AuthProvider},useAuth:function(){return useAuth}});var s=r(7437),o=r(2265),a=r(8081),i=r(6831);let l=(0,o.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,o.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=e=>{let{children:t}=e,[r,n]=(0,o.useState)(null),[d,c]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=(0,a.Aj)(i.I8,e=>{n(e),c(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,a.w7)(i.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return(0,s.jsx)(l.Provider,{value:{user:r,loading:d,logout},children:t})}},6831:function(e,t,r){"use strict";r.d(t,{I8:function(){return c},db:function(){return u},tO:function(){return h}});var s=r(994),o=r(8081),a=r(4086),i=r(5813),l=r(3216);let n={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!n.apiKey||n.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let d=(0,s.ZF)(n),c=(0,o.v0)(d),u=(0,a.ad)(d),h=(0,i.cF)(d);(0,l.$C)(d)}},function(e){e.O(0,[609,15,14,251,971,472,744],function(){return e(e.s=7779)}),_N_E=e.O()}]);